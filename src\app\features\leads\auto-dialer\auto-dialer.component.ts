import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { GridApi, GridOptions } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { ListingStatus } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getPages, hexToRgba } from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';


interface FilterTab {
  key: string;
  label: string;
  count: number;
}

interface LeadData {
  id: string;
  name: string;
  email: string;
  contactNo: string;
  assignedTo: string;
  status: string;
  callStatus: string;
  source: string;
}

@Component({
  selector: 'auto-dialer',
  templateUrl: './auto-dialer.component.html',
})
export class AutoDialerComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  filterTabs: FilterTab[] = [
    { key: 'all', label: 'All', count: 139 },
    { key: 'new', label: 'New', count: 302 },
    { key: 'scheduled', label: 'Scheduled', count: 746 },
    { key: 'overdue', label: 'Overdue', count: 105 },
    { key: 'converted', label: 'Converted', count: 140 }
  ];

  selectedFilter: string = 'all';
  isCountsLoading: boolean = false;
  isAvailable: boolean = true;

  searchTerm: string = '';
  isDataLoading: boolean = false;
  rowData: LeadData[] = [];
  totalCount: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;

  // Grid configuration
  gridOptions: GridOptions;
  gridApi: GridApi;

  // Dialer state
  currentLead: LeadData | null = null;
  currentCallDuration: string = '00:00';
  isDialing: boolean = false;
  isLeadPreviewOpen: boolean = false;

  // Utility functions
  getPages = getPages;
  hexToRgba: Function = hexToRgba;

  globalSettingsData: any;

  constructor(
    public modalRef: BsModalRef,
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private modalService: BsModalService
  ) {
  }

  ngOnInit(): void {
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (Object.keys(data || {}).length) {
          this.globalSettingsData = data;
        }
      });
    this.initializeGridSettings();
    this.loadMockData();
    // Simulate setting a current lead for demo purposes
    setTimeout(() => {
      this.setCurrentLead(this.rowData[0]);
    }, 2000);
  }

  ngOnDestroy(): void {
    this.stopper.emit();
    this.stopper.complete();
  }

  closeModal(): void {
    this.modalRef.hide();
  }

  // Filter methods
  onFilterChange(filterKey: string): void {
    this.selectedFilter = filterKey;
    this.currentPage = 1;
  }

  onAvailabilityToggle(): void {
    console.log('Availability toggled:', this.isAvailable);
    // TODO: Update availability status on server
  }

  // Search methods
  onSearch(): void {
    this.currentPage = 1;
  }

  refreshData(): void {
    console.log('Refreshing data...');
    this.isDataLoading = true;

    // Simulate API call delay
    setTimeout(() => {
      this.loadMockData();
      this.isDataLoading = false;
    }, 500);
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 50;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Lead Name',
        field: 'name',
        valueGetter: (params: any) => [params.data?.name],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Assigned To',
        field: 'assignedTo',
        valueGetter: (params: any) => [params.data?.assignedTo],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
        minWidth: 180,
      },
      {
        headerName: 'Status',
        field: 'status',
        valueGetter: (params: any) => {
          return [
            params?.data?.status?.displayName,
            params?.data?.status?.actionName,
            params?.data?.status?.childType?.displayName,
            params?.data?.status?.childType?.actionName,
          ];
        },
        cellRenderer: (params: any) => {
          const status = params.data.status?.displayName || '';
          const childStatus = params.data.status?.childType?.displayName || '';
          let isNegStatus: boolean;
          isNegStatus = [
            'Overdue',
            'Not Interested',
            'Dropped',
            'Booking Cancel',
            'Pending',
          ].includes(status);
          const statusClass = this.globalSettingsData?.isCustomStatusEnabled
            ? 'text-black'
            : isNegStatus
              ? 'text-danger'
              : 'text-accent-green';

          return `
      <p class="text-truncate fw-600 ${statusClass}">${status === 'Site Visit Scheduled' && this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : status}</p>
      <p class="text-truncate mt-4">${childStatus}</p>
    `;
        },
        minWidth: 120,
      },
      {
        headerName: 'Call Status',
        field: 'callStatus',
        valueGetter: (params: any) => params.data?.callStatus !== 0 ? params.data?.callStatus : '--',
        cellRenderer: (params: any) => {
          if (params.value === '--') {
            return `<span>--</span>`;
          }
          const color = this.getStatusColor(params.value);
          return `
            <span class="status-label-badge" style="background-color: ${this.hexToRgba(
            color,
            0.08
          )};">
              <p class="mr-6" style="color: ${color};">${this.getStatusLabel(params.value)}</p>
            </span>`;
        },
        cellClass: 'cursor-pointer',
        minWidth: 140,
      },
      {
        headerName: 'Source',
        field: 'source',
        valueGetter: (params: any) => [params.data?.source],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
        minWidth: 150,
      },
    ];
    this.gridOptions.context = {
      componentParent: this,
    };
    this.gridOptions.onCellClicked = this.onCellClicked.bind(this);
  }

  getStatusLabel(status: ListingStatus): string {
    return ListingStatus[status] || '';
  }
  getStatusColor(status: ListingStatus): string {
    const statusColorMap: Record<any, string> = {
      [ListingStatus.None]: '',
      [ListingStatus.Draft]: '#6850BF',
      [ListingStatus.Approved]: '#3A6DAF',
      [ListingStatus.Refused]: '#FF0000',
      [ListingStatus.Sold]: '#ED5454',
      [ListingStatus.Archived]: '#50BFA8',
    };
    return statusColorMap[status] || '#000000';
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;
  }

  onCellClicked(event: any): void {
    // Open lead preview when clicking on any cell (similar to manage-leads)
    if (event.data && event.colDef.field) {
      const selectedSection = event.colDef.field === 'status' ? 'Status' : 'Overview';
      this.openLeadPreview(event.data, selectedSection);
    }
  }

  openLeadPreview(leadData: LeadData, selectedSection: string = 'Overview'): void {
    this.isLeadPreviewOpen = true;

    const initialState = {
      data: leadData,
      selectedSection: selectedSection,
      cardData: this.rowData,
      isFromAutoDialer: true,
      currentDialerLead: this.currentLead,
      closeLeadPreviewModal: () => {
        this.isLeadPreviewOpen = false;
        modalRef.hide();
      }
    };

    const modalRef = this.modalService.show(LeadPreviewComponent, {
      class: 'right-modal modal-550 ip-modal-unset',
      initialState: initialState
    });

    // Handle modal close events
    modalRef.onHide?.subscribe(() => {
      this.isLeadPreviewOpen = false;
    });
  }

  loadMockData(): void {
    this.rowData = [
      {
        id: '1',
        name: 'Lead Name01',
        email: '<EMAIL>',
        contactNo: '+1234567890',
        assignedTo: 'Ann George',
        status: 'Call back - Follow up',
        callStatus: 'Pending',
        source: 'Direct/Walk-in'
      },
      {
        id: '2',
        name: 'Marvel Studios',
        email: '<EMAIL>',
        contactNo: '+1234567891',
        assignedTo: 'Maren Levin',
        status: 'New',
        callStatus: 'Completed',
        source: 'LinkedIn'
      },
      {
        id: '3',
        name: 'Tata Steel',
        email: '<EMAIL>',
        contactNo: '+1234567892',
        assignedTo: 'James Vaccaro',
        status: 'Pending',
        callStatus: 'Skipped',
        source: 'MagicBricks'
      },
      {
        id: '4',
        name: 'Blackrock',
        email: '<EMAIL>',
        contactNo: '+1234567893',
        assignedTo: 'Chandler Curtis',
        status: 'Call Back',
        callStatus: 'Failed',
        source: 'Direct/Walk-in'
      },
      {
        id: '5',
        name: 'Hyper Lab',
        email: '<EMAIL>',
        contactNo: '+1234567894',
        assignedTo: 'Talan Passaquindici',
        status: 'Call Back',
        callStatus: 'Retry Scheduled',
        source: 'Direct/Walk-in'
      }
    ];
    this.totalCount = this.rowData.length;

    // Update grid data if grid API is available
    if (this.gridApi) {
      this.gridApi.setRowData(this.rowData);
    }
  }

  onPageChange(event: any): void {
    this.currentPage = event.offset + 1;
  }

  // Utility methods for styling
  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'new':
        return 'badge-success';
      case 'pending':
        return 'badge-warning';
      case 'call back':
        return 'badge-info';
      case 'converted':
        return 'badge-primary';
      default:
        return 'badge-secondary';
    }
  }

  getCallStatusClass(callStatus: string): string {
    switch (callStatus?.toLowerCase()) {
      case 'completed':
        return 'badge-success';
      case 'pending':
        return 'badge-warning';
      case 'failed':
        return 'badge-danger';
      case 'skipped':
        return 'badge-secondary';
      case 'retry scheduled':
        return 'badge-info';
      default:
        return 'badge-light';
    }
  }

  getSourceIcon(source: string): string {
    switch (source?.toLowerCase()) {
      case 'linkedin':
        return 'ic-linkedin icon ic-xs text-primary';
      case 'magicbricks':
        return 'ic-magicbricks icon ic-xs text-danger';
      case 'direct/walk-in':
        return 'ic-user icon ic-xs text-secondary';
      default:
        return 'ic-globe icon ic-xs text-muted';
    }
  }

  // Dialer methods
  callLead(): void {
    if (this.currentLead) {
      console.log('Calling lead:', this.currentLead.name);
      this.isDialing = true;
      this.startCallTimer();
    }
  }

  skipLead(): void {
    console.log('Skipping lead');
    this.currentLead = null;
    this.isDialing = false;
    // TODO: Move to next lead
  }

  endCall(): void {
    console.log('Ending call');
    this.currentLead = null;
    this.isDialing = false;
    this.currentCallDuration = '00:00';
  }

  private startCallTimer(): void {
    // TODO: Implement call timer
    this.currentCallDuration = '00:30';
  }

  // Mock method to simulate current lead
  setCurrentLead(lead: LeadData): void {
    this.currentLead = lead;
  }
}
