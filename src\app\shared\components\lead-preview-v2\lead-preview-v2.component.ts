import { Component, EventEmitter, Input, OnInit, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { combineLatest, filter, map, skipWhile, Subscription, take, takeUntil } from 'rxjs';
import { EMPTY_GUID, UPDATE_STATUS_PAST_TENSE } from 'src/app/app.constants';
import { BHKType, EnquiryType, FurnishStatus, LeadSource, OfferType, Profession, PurposeType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { convertUrlsToLinks, formatBudget, getAssignedToDetails, getBHKDisplayString, getLocationDetailsByObj, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { CustomStatusChangeComponent } from 'src/app/features/leads/custom-status-change/custom-status-change.component';
import { StatusChangeComponent } from 'src/app/features/leads/status-change/status-change.component';
import { FetchTagsList } from 'src/app/reducers/custom-tags/custom-tags.actions';
import { getTagsList } from 'src/app/reducers/custom-tags/custom-tags.reducer';
import { getGlobalAnonymousIsLoading, getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { ClearCardData, FetchLeadList, UpdateLeadsTagInfo } from 'src/app/reducers/lead/lead.actions';
import { getIsLeadCustomStatusEnabled, getLeadCardData, getLeads } from 'src/app/reducers/lead/lead.reducer';
import { LeadPreviewChanged, LeadPreviewSaved } from 'src/app/reducers/loader/loader.actions';
import { getLeadPreviewChanged } from 'src/app/reducers/loader/loader.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { getAdminsAndReportees, getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'lead-preview-v2',
  templateUrl: './lead-preview-v2.component.html',
})
export class LeadPreviewV2Component implements OnInit {
  @Input() clickedData: any = null;
  @Input() whatsAppComp: boolean = false;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('statusChangeComponent', { static: false })
  statusChangeComponent: StatusChangeComponent;
  @ViewChild('customStatusChangeComponent', { static: false })
  customStatusChangeComponent: CustomStatusChangeComponent;
  initialState: any;
  data: any;
  users: any;
  selectedSection: any = 'Overview';
  showOnlyPopup: boolean = true;
  callRecordingDetails: {
    years: any;
    yearData: { months: any; monthData: { date: string; audioUrl: any }[] }[];
  }[];
  moment = moment;
  formatBudget = formatBudget;
  getAssignedToDetails = getAssignedToDetails;
  getBHKDisplayString = getBHKDisplayString;
  getLocationDetailsByObj = getLocationDetailsByObj;
  getTimeZoneDate = getTimeZoneDate;
  OfferType = OfferType;
  FurnishStatus = FurnishStatus;
  convertUrlsToLinks = convertUrlsToLinks
  BHKType = BHKType;
  LeadSource = LeadSource;
  PurposeType = PurposeType;
  EnquiryType = EnquiryType;
  EMPTY_GUID = EMPTY_GUID;
  navigationItems = [
    { label: 'GLOBAL.overview', section: 'Overview' },
    { label: 'GLOBAL.status', section: 'Status' },
    { label: 'GLOBAL.history', section: 'History' },
    { label: 'TASK.notes', section: 'Notes' },
    { label: 'GLOBAL.document', section: 'Document' },
  ];
  flagOptions: any[] = [];
  selectedFlags: any;
  toggleSelectFlag: boolean = false;
  cardData: any;
  foundData: any;
  defaultCurrency: string = 'INR';
  isLeadPreviewChanged: boolean = false;
  fetchLeadsWhenClosed: boolean = false;
  dataUpdated$: Subscription;
  isNextDataLoading: boolean = false;
  isPreviousDataLoading: boolean = false;
  canEditLead: boolean = false;
  canEditTags: boolean = false;
  canUpdateInvoice: boolean = false;
  closeLeadPreviewModal: any;
  globalSettingsData: any;
  canViewLeadSource: boolean = false;
  canUpdateStatus: boolean = false;
  canUpdateBookedLead: boolean = false;
  flagOptionsDefault: any = [];
  currentPath: string;
  isCustomStatusEnabled: boolean = false;
  isGlobalSettingsLoading: boolean = true;
  userData: any;

  get canShowStatusPopupInPreview(): boolean {
    return (
      ((this.data.status?.displayName ===
        UPDATE_STATUS_PAST_TENSE['meeting-scheduled'] ||
        this.data.status?.displayName ===
        UPDATE_STATUS_PAST_TENSE['visit-scheduled'] ||
        this.data.status?.displayName ===
        UPDATE_STATUS_PAST_TENSE['referral-scheduled']) &&
        !this.isCustomStatusEnabled) ||
      (this.isCustomStatusEnabled &&
        this.shouldOpenAppointmentPageForStatus(this.data?.status))
    );
  }

  shouldOpenAppointmentPageForStatus(status: any): boolean {
    return (
      status?.shouldOpenAppointmentPage || status?.childType?.shouldOpenAppointmentPage ||
      (status?.childType?.length && status?.childType?.some((childType: any) => childType?.shouldOpenAppointmentPage)) ||
      false
    );
  }

  get isMobileView(): boolean {
    return window.innerWidth <= 480;
  }
  get isFirstPage(): boolean {
    return this.shareDataService.currentPageNumber === 1;
  }
  get currentPage(): number {
    return this.shareDataService.currentPageNumber;
  }
  get totalPages(): number {
    return this.shareDataService.totalPages;
  }
  get isLastLead(): boolean {
    return (
      (this.foundData == this.cardData?.length - 1 && this.isMobileView) ||
      (this.totalPages === this.currentPage &&
        this.foundData == this.cardData?.length - 1)
    );
  }

  get enquiryTypes(): string {
    return (
      this.data?.enquiry?.enquiryTypes
        ?.map((enquiry: any) => EnquiryType[enquiry])
        ?.join(', ') || '--'
    );
  }

  get bhkTypes(): string {
    return (
      this.data?.enquiry?.bhkTypes
        ?.map((type: any) => BHKType[type])
        ?.join(', ') || '--'
    );
  }

  get bhkNo(): string {
    // return this.globalSettingsData?.isCustomLeadFormEnabled
    //   ? this.data?.enquiry?.bhKs
    //     /* BR field commented out
    //     ?.map((bhk: any) => getBRDisplayString(bhk))
    //     */
    //     ?.join(', ')
    //   :
    return this.data?.enquiry?.bhKs
      ?.map((bhk: any) => getBHKDisplayString(bhk))
      ?.join(', ');
  }


  get beds(): string {
    return Array.isArray(this.data.enquiry?.beds)
      ? this.data.enquiry?.beds.map((bed: any) => (bed === 0 || bed === '0' ? 'Studio' : bed))
      : [];
  }

  get addresses(): string {
    return (
      this.data?.enquiry?.addresses
        ?.map((address: any) => getLocationDetailsByObj(address))
        ?.join('; ') || '--'
    );
  }

  constructor(
    public modalService: BsModalService,
    public modalRef: BsModalRef,
    private store: Store<AppState>,
    public gridOptionService: GridOptionsService,
    public router: Router,
    private shareDataService: ShareDataService,
    private trackingService: TrackingService

  ) { }

  async ngOnInit(): Promise<void> {
    this.shareDataService.backtoOverview.subscribe((value) => {
      if (value) {
        this.selectedSection = value;
      }
    });

    this.store.select(getLeads)
      .pipe(takeUntil(this.stopper), skipWhile(() => this.isMobileView))
      .subscribe(data => {
        this.cardData = data;
        // this.data = this.cardData?.find((data: any) => data?.id === this.data?.id)
      });

    this.store.select(getLeadCardData)
      .pipe(takeUntil(this.stopper), skipWhile(() => !this.isMobileView))
      .subscribe(async (data: any) => {
        this.cardData = data;
        // this.data = this.cardData?.find((data: any) => data?.id === this.data?.id)
      });

    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;

    await this.store
      .select(getGlobalAnonymousIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    this.isCustomStatusEnabled = await this.store
      .select(getIsLeadCustomStatusEnabled)
      .pipe(
        map((data: any) => data),
        take(1)
      )
      .toPromise();

    this.selectedFlags = this.data?.customFlags;
    this.store.dispatch(new FetchTagsList());
    this.store
      .select(getTagsList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        let flagData = data || [];
        if (flagData.length > 0) {
          this.flagOptions = flagData
            .filter((flag: any) => flag?.isActive)
            .map((flag: any) => {
              const isActive = false;
              return { ...flag, isActive };
            });
          this.flagOptionsDefault = this.flagOptions;
        }
        this.sortFlags();
        this.updateFlags();
      });

    this.foundData = this.cardData?.findIndex((item: any) =>
      this.isDataEqual(item, this.data)
    );
    if (
      (this.foundData === this.cardData?.length - 3 ||
        this.foundData === this.cardData?.length - 1) &&
      this.isMobileView
    ) {
      this.shareDataService.emitFetchNextLeads();
    }


    this.getCardData();

    const adminsAndReportees$ = this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper));
    const allUsers$ = this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper));
    const permissions$ = this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper));
    this.store
      .select(getLeadPreviewChanged)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isLeadPreviewChanged = data;
      });
    combineLatest({
      adminsAndReportees: adminsAndReportees$,
      allUsers: allUsers$,
      permissions: permissions$,
    }).subscribe(({ adminsAndReportees, allUsers, permissions }) => {
      const permissionsSet = new Set(permissions);
      if (permissions?.includes('Permissions.Leads.ViewLeadSource'))
        this.canViewLeadSource = true;
      if (permissions?.includes('Permissions.Leads.UpdateLeadStatus'))
        this.canUpdateStatus = true;
      if (permissions?.includes('Permissions.Users.AssignToAny')) {
        this.users = allUsers;
      } else {
        this.users = adminsAndReportees;
      }
      if (permissions?.includes('Permissions.Leads.Update')) {
        this.canEditLead = true;
      }
      this.canEditTags = permissionsSet.has('Permissions.Leads.UpdateTags');
      if (permissions?.includes('Permissions.Invoice.Update')) {
        this.canUpdateInvoice = true;
      }
      if (permissions?.includes('Permissions.Invoice.View')) {
        if (
          !this.canUpdateInvoice &&
          this.currentPath === '/invoice' &&
          this.data?.status?.status === 'invoiced'
        ) {
          this.selectedSection = 'Overview';
        }
      }
    });
    if (this.initialState) {
      this.selectedSection = this.initialState.selectedSection;
    }

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
        this.globalSettingsData = data;
      });
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Leads.UpdateBookedLead')) {
          this.canUpdateBookedLead = true;
        }
      });
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.clickedData && changes.clickedData.currentValue) {
      this.data = this.clickedData;
    }
  }

  ngAfterViewInit(): void {
    const swipeArea = document.getElementById('swipeArea');
    let startX: any, startY: any;

    swipeArea.addEventListener('touchstart', (event) => {
      startX = event.touches[0].clientX;
      startY = event.touches[0].clientY;
    });

    swipeArea.addEventListener('touchend', (event) => {
      const endX = event.changedTouches[0].clientX;
      const endY = event.changedTouches[0].clientY;

      const deltaX = endX - startX;
      const deltaY = endY - startY;
      // left to right swipe
      if (deltaX > 100 && Math.abs(deltaY) < 50) {
        this.prevData();
      }
      // right to left swipe
      else if (deltaX < -100 && Math.abs(deltaY) < 50) {
        this.nextData();
      }
    });
  }

  isSelectedFlag(f: any): boolean {
    return this.selectedFlags?.some(
      (selectedFlag: any) => selectedFlag?.flag.id === f.id
    );
  }

  filteredNavigationItems() {
    if (this.data?.isArchived) {
      return this.navigationItems.filter((item) =>
        ['Overview', 'History'].includes(item.section)
      );
    }
    if (this.currentPath === '/invoice') {
      return this.canUpdateInvoice
        ? this.navigationItems
        : this.navigationItems.filter((item) => item.section !== 'Status');
    } else {
      if (this.data?.status?.status === 'invoiced') {
        return this.navigationItems.filter((item) => item.section !== 'Status');
      } else {
        return this.canUpdateStatus
          ? this.navigationItems
          : this.navigationItems.filter((item) => item.section !== 'Status');
      }
    }
  }

  getActiveBackgroundColor(flag: any): any {
    const bgColor: any = {};
    if (flag.isActive) {
      bgColor['border-black fw-600'] = true;
      bgColor['bg-white'] = true;
    }
    return bgColor;
  }

  isDataEqual(obj1: any, obj2: any): boolean {
    return obj1.id === obj2.id;
  }

  prevData() {
    if (this.foundData > 0) {
      this.foundData--;
      this.data = this.cardData?.[this.foundData];
      this.updateFlags();
    } else if (this.foundData === 0 && !this.isMobileView) {
      this.foundData = 0;
      this.shareDataService.emitFetchPreviousLeads(false);
      this.subscribeToDataUpdated()
      this.isPreviousDataLoading =
        this.shareDataService.currentPageNumber === 1 ? false : true;
    }
    if (this.selectedSection === 'Status')
      this.isCustomStatusEnabled
        ? this.customStatusChangeComponent?.cleanStatusForm()
        : this.statusChangeComponent.cleanStatusForm();
    this.trackingService.trackFeature(`Web.Leads.Menu.Previouslead.Click`, this.data.id);
  }

  nextData() {
    if (this.foundData < this.cardData?.length - 1) {
      this.foundData++;
      this.data = this.cardData?.[this.foundData];
      this.updateFlags();
      if (
        (this.foundData === this.cardData?.length - 3 ||
          this.foundData === this.cardData?.length - 1) &&
        !this.isNextDataLoading &&
        this.isMobileView
      ) {
        this.shareDataService.emitFetchNextLeads();
        this.subscribeToDataUpdated()
        this.isNextDataLoading = true;
      }
    } else if (
      this.foundData === this.cardData?.length - 1 &&
      !this.isMobileView
    ) {
      this.foundData = 0;
      this.shareDataService.emitFetchNextLeads(false);
      this.subscribeToDataUpdated()
      this.isNextDataLoading = true;
    }
    if (this.selectedSection === 'Status')
      this.statusChangeComponent?.cleanStatusForm();
    this.trackingService.trackFeature(`Web.Leads.Menu.NextLead.Click`, this.data.id);
  }

  getNextLead() {
    this.shareDataService.sendMiniBookingformData(this.data);
  }

  updateFlags() {
    this.flagOptions = this.flagOptionsDefault;
    const flags: any = {};
    this.data?.customFlags?.forEach((flag: any) => {
      flags[flag?.flag?.id] = true;
    });
    this.flagOptions = this.flagOptions?.map((flag: any) => {
      if (flags[flag?.id]) {
        return { ...flag, isActive: true };
      }
      return { ...flag };
    });
  }

  getUserName(id: string): string {
    return getAssignedToDetails(id, this.users, true) || '';
  }

  getProfession(profession: Profession): string {
    return Profession[profession];
  }

  onSectionSelect(section: string) {
    this.selectedSection = section;
    this.trackingService.trackFeature(`Web.Leads.Menu.${this.selectedSection.replace(/\s+/g, '')}.Click`, this.data.id);
  }

  flagAction(flag: any): void {
    flag.isActive = !flag?.isActive;
    let payload = {
      id: this.data.id,
      flagId: flag?.id,
    };
    this.flagOptions = this.flagOptions.map((f: any) =>
      f.id === flag.id ? { ...f, isActive: flag.isActive } : f
    );
    this.store.dispatch(new LeadPreviewChanged());
    this.store.dispatch(new UpdateLeadsTagInfo(payload, this.data.id, false));
    this.trackingService.trackFeature(`Web.Leads.Button.Tags.Click`, this.data.id);
  }

  sortFlags() {
    this.flagOptions.sort((a, b) => {
      if (a.isActive === b.isActive) {
        const nameA = a.name.toUpperCase();
        const nameB = b.name.toUpperCase();
        return nameA.localeCompare(nameB);
      } else {
        return a.isActive ? -1 : 1;
      }
    });
  }

  updateNotes(data: any[]): void {
    this.data.notes = data;
  }

  checkStatus(section: string) {
    this.onSectionSelect(section);
    this.trackingService.trackFeature(`Web.Leads.Button.ChangeStatus.Click`, this.data.id);
  }

  openAudioPlayer(aP: TemplateRef<any>) {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    if (this.data?.callRecordingUrls) {
      this.callRecordingDetails = Object.entries(this.data?.callRecordingUrls)
        .map((item: any) => {
          return {
            years: item[0],
            yearData: Object.entries(item[1])
              .reverse()
              .map((item1: any) => {
                return {
                  months: item1[0],
                  monthData: Object.keys(item1[1])
                    .reverse()
                    .map((key) => {
                      let audioDate = key.toString();
                      let audioFile = item1[1][key];
                      return { date: audioDate, audioUrl: audioFile };
                    }),
                };
              }),
          };
        })
        .reverse();
    }
    this.modalRef = this.modalService.show(aP, {
      class: 'right-modal modal-350 ph-modal-unset',
    });
  }

  getCardData() {
    if (this.foundData || this.cardData?.length) {
      return;
    }
    let { cardData } = this.shareDataService.getCardData();
    this.cardData = cardData;
    this.foundData = this.cardData?.findIndex((item: any) =>
      this.isDataEqual(item, this.data)
    );
    this.isNextDataLoading = false;
    this.isPreviousDataLoading = false;
  }

  hasPlotSubType(propertyTypes: any[]): boolean {
    return propertyTypes?.some(type => type?.childType?.displayName === 'Plot');
  }

  getSubtypesTitle(): string {
    return this.data?.enquiry?.propertyTypes?.map((type: any) => type?.childType?.displayName).join(', ') || '';
  }

  subscribeToDataUpdated() {
    this.dataUpdated$ = this.shareDataService.dataUpdated.subscribe(
      ({ cardData, isPrevFetched }) => {
        if (cardData?.length) {
          this.cardData = cardData;
          if (!this.isMobileView) {
            const index = isPrevFetched ? cardData.length - 1 : 0;
            this.data = cardData[index];
            this.foundData = index;
          }
          this.isNextDataLoading = false;
          this.isPreviousDataLoading = false;
          this.dataUpdated$.unsubscribe();
        }
      }
    );
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
    this.shareDataService.gotoOverviewTab(null);
    if (this.isLeadPreviewChanged || this.fetchLeadsWhenClosed) {
      this.store.dispatch(new ClearCardData())
      this.fetchLeadsWhenClosed = false;
      this.store.dispatch(
        new FetchLeadList(true, location?.href?.includes('invoice'))
      );
    }
    this.store.dispatch(new LeadPreviewSaved());
    this.dataUpdated$?.unsubscribe();
  }
}
