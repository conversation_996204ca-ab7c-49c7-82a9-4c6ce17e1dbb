import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { assignToSort } from 'src/app/core/utils/common.util';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchAdminsAndReportees, FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getAdminsAndReportees, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'automation-settings',
  templateUrl: './automation-settings.component.html',
})
export class AutomationSettingsComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  isAutoDialerOpen: boolean = false;
  selectedLeadSequence: string = 'nextLeads'; // Default to 'Continue With Next Lead(s)'
  autoDialerForm: FormGroup;
  canAssignToAny: boolean;
  activeUsers: any = [];
  allActiveUsers: any = [];

  constructor(
    private router: Router,
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    private fb: FormBuilder,
    private store: Store<AppState>,
  ) {
    this.autoDialerForm = this.fb.group({
      selectedUsers: [null],
      callInterval: [null]
    });
  }

  ngOnInit(): void {
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
          this.store.dispatch(new FetchUsersListForReassignment());
        } else {
          this.store.dispatch(new FetchAdminsAndReportees());
        }
      });

    this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      });
  }

  navigateToReportAutomation(): void {
    this.router.navigate(['/global-config/report-automation']);
  }

  cancelAutoDialer(): void {
    // Reset form or close the auto dialer section
    this.isAutoDialerOpen = false;
    // Reset to default values
    this.selectedLeadSequence = 'nextLeads';
    // Reset the form
    this.autoDialerForm.reset();
  }

  saveAutoDialerSettings(): void {
    // Get form values
    const formValues = this.autoDialerForm.value;

    // Implement save logic here
    const autoDialerSettings: any = {
      selectedUsers: formValues.selectedUsers || [],
      callInterval: formValues.callInterval,
      leadSequenceConfiguration: this.selectedLeadSequence
    };

    console.log('Saving Auto Dialer Settings:', autoDialerSettings);

    // TODO: Dispatch action to save settings
    // this.store.dispatch(new SaveAutoDialerSettings(autoDialerSettings));

    // Show success message
    // this.notificationService.success('Auto Dialer settings saved successfully');

    // Close the section
    this.isAutoDialerOpen = false;
  }
}
