<div class="flex-between bg-white border-gray-y">
    <div class="align-center no-validation">
        <ul class="py-12 d-flex">
            <li *ngFor="let filter of filterTabs">
                <a class="position-relative header-5 ml-30 fw-semi-bold cursor-pointer"
                    [class.active]="selectedFilter === filter.key" (click)="onFilterChange(filter.key)">
                    <span>{{ filter.label }}</span>
                    <span *ngIf="!isCountsLoading">
                        ({{ filter.count }})
                    </span>
                    <span *ngIf="isCountsLoading">
                        (<div class="container px-4 d-inline">
                            <ng-container *ngFor="let dot of [1,2,3]">
                                <div class="dot-falling"></div>
                            </ng-container>
                        </div>)
                    </span>
                </a>
            </li>
        </ul>
    </div>

    <!-- Availability Toggle -->
    <div class="align-center mr-30">
        <div class="align-center">
            <span class="mr-10 text-sm text-muted">available</span>
            <input type="checkbox" class="toggle-switch toggle-availability" [(ngModel)]="isAvailable"
                (change)="onAvailabilityToggle()" id="availabilityToggle">
            <label for="availabilityToggle" class="switch-label"></label> <span
                class="mr-10 text-sm text-muted">unavailable</span>

        </div>
    </div>
</div>

<!-- Search Section -->
<div class="pt-16 px-30">
    <div class="align-center bg-white w-100 border-gray">
        <div class="align-center flex-grow-1 no-validation border-end">
            <div class="position-relative flex-grow-1">
                <div class="align-center w-100 px-10 py-12">
                    <span class="search icon ic-search ic-sm ic-slate-90 mr-12"></span>
                    <input type="text" placeholder="type to search" [(ngModel)]="searchTerm"
                        (keydown.enter)="onSearch()" [ngModelOptions]="{standalone: true}"
                        class="border-0 outline-0 w-100" />
                </div>
            </div>
            <div class="flex-end">
                <div class="bg-dark text-white px-20 py-12 align-center cursor-pointer" (click)="refreshData()">
                    <span class="ic-update icon mr-8 ic-xxs"></span>
                    Refresh
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Data Grid -->
<div class="px-30">
    <ng-container>
        <div class="manage-reference pinned-grid">
            <ag-grid-angular class="ag-theme-alpine" #agGrid [gridOptions]="gridOptions"
                (gridReady)="onGridReady($event)" [rowData]="rowData" [suppressPaginationPanel]="true"
                [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true">
            </ag-grid-angular>
        </div>

        <!-- Pagination -->
        <div class="flex-end my-20">
            <div class="mr-10">Showing
                {{ (currentPage - 1) * pageSize + 1 }}
                to
                {{ (currentPage - 1) * pageSize + rowData?.length }}
                of {{ totalCount }} entries
            </div>
            <pagination [offset]="currentPage - 1" [limit]="1" [range]="1" [size]="getPages(totalCount, pageSize)"
                (pageChange)="onPageChange($event)">
            </pagination>
        </div>
    </ng-container>
</div>

<!-- No Data Template -->
<ng-template #noData>
    <div class="flex-center-col h-300">
        <img src="assets/images/layered-cards.svg" alt="No Data Found">
        <div class="header-3 fw-600 text-center mt-20">No leads found</div>
        <div class="text-muted text-center mt-8">Try adjusting your filters or search criteria</div>
    </div>
</ng-template>

<!-- Loading Template -->
<ng-template #dataLoader>
    <div class="flex-center h-300">
        <application-loader></application-loader>
    </div>
</ng-template>

<!-- Floating Dialer Widget -->
<div class="position-fixed bottom-20 z-index-1000" [ngClass]="isLeadPreviewOpen ? 'right-580' : 'right-20'"
    *ngIf="currentLead">
    <div class="bg-dark text-white border shadow-lg br-8 p-16 min-w-300">
        <div class="d-flex align-center mb-12">
            <div class="bg-success brtl-8 brbl-8 p-8 mr-12 d-flex align-center justify-content-center">
                <span class="ic-phone icon ic-sm text-white"></span>
            </div>
            <div class="flex-grow-1">
                <div class="fw-600 text-white mb-4">{{ currentLead?.name || 'Lead Name01' }}</div>
                <div class="text-xs text-light-gray">
                    <span class="ic-user icon ic-xs mr-4"></span>
                    {{ currentLead?.assignedTo || 'Ann George' }}
                </div>
            </div>
        </div>
        <div class="flex-between">
            <div class="bg-secondary text-dark br-20 px-12 py-6">
                <span class="text-xs fw-600">{{ currentLead?.status || 'Call back - Follow up' }}</span>
            </div>
            <div class="align-center ml-auto">
                <button class="btn btn-sm btn-danger br-50 p-8" (click)="endCall()" title="End Call">
                    <span class="ic-phone icon ic-xs"></span>
                </button>
            </div>
        </div>
    </div>
</div>